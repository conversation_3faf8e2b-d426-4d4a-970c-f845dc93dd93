#!/usr/bin/env python3
"""
Simplified YouTube Transcriber - Debug Version
Focus on core functionality to identify issues.
"""

import logging
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)

try:
    import whisper
    import yt_dlp
except ImportError as e:
    print(f"Missing dependency: {e}")
    print("Install with: pip install openai-whisper yt-dlp")
    exit(1)

def sanitize_filename(filename: str) -> str:
    """Sanitize filename for filesystem compatibility."""
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    return filename[:200].strip()

def extract_video_metadata(url: str) -> Dict[str, Any]:
    """Extract video metadata using yt-dlp."""
    ydl_opts = {'quiet': True, 'no_warnings': True}
    
    try:
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(url, download=False)
            
            metadata = {
                'title': info.get('title', 'Unknown Title'),
                'uploader': info.get('uploader', 'Unknown Author'),
                'upload_date': info.get('upload_date', ''),
                'url': url,
            }
            
            # Format upload date
            if metadata['upload_date']:
                try:
                    date_obj = datetime.strptime(metadata['upload_date'], '%Y%m%d')
                    metadata['formatted_date'] = date_obj.strftime('%Y-%m-%d')
                except:
                    metadata['formatted_date'] = metadata['upload_date']
            else:
                metadata['formatted_date'] = ''
            
            return metadata
    except Exception as e:
        logging.error(f"Error extracting metadata: {e}")
        return {
            'title': 'Unknown Title',
            'uploader': 'Unknown Author',
            'upload_date': '',
            'formatted_date': '',
            'url': url,
        }

def download_youtube_audio(url: str, output_dir: Path) -> Optional[Path]:
    """Download audio from YouTube URL."""
    ydl_opts = {
        'format': 'bestaudio/best',
        'postprocessors': [{
            'key': 'FFmpegExtractAudio',
            'preferredcodec': 'm4a',
        }],
        'outtmpl': str(output_dir / '%(title)s.%(ext)s'),
        'noplaylist': True,
    }
    
    try:
        # Get files before download
        files_before = set(output_dir.glob('*'))
        
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            logging.info("Downloading audio...")
            ydl.download([url])
        
        # Find new files
        files_after = set(output_dir.glob('*'))
        new_files = files_after - files_before
        
        # Find audio file
        audio_extensions = {'.m4a', '.mp3', '.wav', '.aac', '.ogg'}
        for new_file in new_files:
            if new_file.suffix.lower() in audio_extensions:
                logging.info(f"Downloaded: {new_file.name}")
                return new_file
        
        logging.error("No audio file found after download")
        return None
        
    except Exception as e:
        logging.error(f"Download error: {e}")
        return None

def transcribe_audio(audio_path: Path, metadata: Dict[str, Any], model) -> Optional[Path]:
    """Transcribe audio file."""
    logging.info(f"Transcribing: {audio_path.name}")
    
    try:
        # Check file
        if not audio_path.exists():
            logging.error("Audio file does not exist")
            return None
        
        file_size = audio_path.stat().st_size
        logging.info(f"File size: {file_size} bytes")
        
        if file_size == 0:
            logging.error("Audio file is empty")
            return None
        
        # Transcribe
        logging.info("Starting Whisper transcription...")
        result = model.transcribe(str(audio_path))
        logging.info("Transcription completed")
        
        # Get segments
        segments = result.get('segments', [])
        text = result.get('text', '').strip()
        
        logging.info(f"Found {len(segments)} segments")
        logging.info(f"Total text length: {len(text)} characters")
        
        if text:
            logging.info(f"Sample text: {text[:100]}...")
        
        # Create output file
        safe_title = sanitize_filename(metadata['title'])
        output_path = audio_path.parent / f"{safe_title}.md"
        
        with open(output_path, 'w', encoding='utf-8') as f:
            # YAML front matter
            f.write("---\n")
            f.write(f"date: {metadata['formatted_date']}\n")
            f.write(f"title: {metadata['title']}\n")
            f.write(f"author: {metadata['uploader']}\n")
            f.write(f"url: {metadata['url']}\n")
            f.write("---\n\n")
            
            if not segments:
                f.write("- [00:00:00]() - [No Content] - No speech detected\n\n")
            else:
                # Simple approach: write all text as one chunk
                f.write(f"- [00:00:00]({metadata['url']}) - [Full Transcript] - {text}\n\n")
        
        logging.info(f"Transcript saved: {output_path}")
        return output_path
        
    except Exception as e:
        logging.error(f"Transcription error: {e}")
        return None

def process_url(url: str, output_dir: Path) -> bool:
    """Process a single YouTube URL."""
    logging.info(f"Processing: {url}")
    
    try:
        # Load Whisper model
        logging.info("Loading Whisper model...")
        model = whisper.load_model("base")  # Use smaller model for testing
        logging.info("Model loaded")
        
        # Get metadata
        metadata = extract_video_metadata(url)
        logging.info(f"Video: {metadata['title']}")
        
        # Download audio
        audio_path = download_youtube_audio(url, output_dir)
        if not audio_path:
            return False
        
        # Transcribe
        transcript_path = transcribe_audio(audio_path, metadata, model)
        
        # Cleanup
        if audio_path.exists():
            audio_path.unlink()
            logging.info("Audio file deleted")
        
        return transcript_path is not None
        
    except Exception as e:
        logging.error(f"Processing error: {e}")
        return False

def main():
    """Main function."""
    logging.info("=== Simple YouTube Transcriber ===")
    
    # Get URL
    url = input("Enter YouTube URL: ").strip()
    if not url:
        logging.error("No URL provided")
        return
    
    # Get output directory
    output_dir = Path.cwd()
    logging.info(f"Output directory: {output_dir}")
    
    # Process
    success = process_url(url, output_dir)
    
    if success:
        logging.info("✓ Processing completed successfully")
    else:
        logging.error("✗ Processing failed")

if __name__ == '__main__':
    main()

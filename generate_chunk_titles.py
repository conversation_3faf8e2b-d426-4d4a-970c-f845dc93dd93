#!/usr/bin/env python3

import re
import requests
import logging
from pathlib import Path
from tkinter import filedialog, Tk
from typing import Optional

# --- Configuration ---
LLM_BASE_URL = "http://127.0.0.1:7856"
LLM_MODEL = "qwen3-30b-a3b"
PROTECTED_TITLE_PREFIX = "-"  # Titles starting with this character will not be overwritten

# --- Setup Logging ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)

def choose_folder() -> Optional[Path]:
    """Opens a dialog to choose a folder."""
    root = Tk()
    root.withdraw()  # Hide the main tkinter window
    folder_path = filedialog.askdirectory(title="Select Folder Containing Transcript Files")
    root.destroy()  # Destroy the root window after selection
    if folder_path:
        logging.info(f"Selected folder: {folder_path}")
        return Path(folder_path)
    else:
        logging.warning("No folder selected.")
        return None

def generate_title_with_llm(text: str) -> str:
    """Generate a concise title for the given text using the local LLM."""
    if not text.strip():
        return "No Content"

    try:
        # Prepare the prompt for title generation
        prompt = f"""Create a concise, descriptive title (5-20 words) for this transcript segment. The title should capture the main topic or key concept discussed. Return only the title on a single line, no quotes, no extra text, no newlines.

Transcript: {text.strip()}

Title:"""

        # Make request to local LLM
        response = requests.post(
            f"{LLM_BASE_URL}/v1/chat/completions",
            headers={"Content-Type": "application/json"},
            json={
                "model": LLM_MODEL,
                "messages": [
                    {"role": "user", "content": prompt}
                ],
                "max_tokens": 1000,
                "temperature": 0.5
            },
            timeout=30
        )

        if response.status_code == 200:
            result = response.json()
            raw_content = result["choices"][0]["message"]["content"].strip()

            # Remove thinking tags and content
            title = re.sub(r'<think>.*?</think>', '', raw_content, flags=re.DOTALL).strip()

            # Clean up the title - remove quotes and extra formatting
            title = title.strip('"\'').strip()

            # Ensure title is a single line - replace any newlines with spaces
            title = re.sub(r'\s+', ' ', title).strip()

            # Limit title length to reasonable size (optional safety measure)
            if len(title) > 100:
                title = title[:97] + "..."

            return title if title else "Untitled Segment"
        else:
            logging.warning(f"LLM request failed with status {response.status_code}")
            return "Untitled Segment"

    except Exception as e:
        logging.warning(f"Failed to generate title with LLM: {e}")
        return "Untitled Segment"

def test_llm_connection() -> bool:
    """Test if the LLM server is accessible."""
    try:
        response = requests.get(f"{LLM_BASE_URL}/v1/models", timeout=5)
        if response.status_code == 200:
            logging.info("LLM server is accessible.")
            return True
        else:
            logging.error(f"LLM server returned status {response.status_code}")
            return False
    except Exception as e:
        logging.error(f"Cannot connect to LLM server: {e}")
        return False

def process_transcript_file(file_path: Path) -> bool:
    """Process a single transcript file to generate titles."""
    logging.info(f"Processing: {file_path.name}")

    try:
        # Read the file
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Split content into segments using regex to handle multi-line titles
        # Pattern to match complete transcript segments including multi-line titles
        segment_pattern = r'- \[(\d{2}:\d{2}:\d{2})\]\(\) - \[(.+?)\] - (.+?)(?=\n- \[|\n\n|\Z)'

        segments = re.findall(segment_pattern, content, re.DOTALL)

        if not segments:
            logging.warning(f"No transcript segments found in {file_path.name}")
            return False

        modified = False
        new_content_parts = []

        # Process YAML front matter first
        yaml_end = content.find('---', 3)  # Find second occurrence of ---
        if yaml_end != -1:
            yaml_content = content[:yaml_end + 3]
            new_content_parts.append(yaml_content)
            new_content_parts.append("")  # Empty line after YAML

        for timestamp, current_title, text in segments:
            # Clean up title and text (remove extra whitespace and newlines)
            current_title = re.sub(r'\s+', ' ', current_title.strip())
            text = re.sub(r'\s+', ' ', text.strip())

            # Skip titles that are protected (start with the protected prefix)
            if current_title.startswith(PROTECTED_TITLE_PREFIX):
                logging.info(f"Skipping protected title at {timestamp}: {current_title[:50]}...")
                new_content_parts.append(f"- [{timestamp}]() - [{current_title}] - {text}")
                new_content_parts.append("")  # Empty line after segment
                continue

            # Process all other titles (overwrite existing ones)
            logging.info(f"Generating title for segment at {timestamp}...")
            new_title = generate_title_with_llm(text)

            # Add the updated segment
            new_content_parts.append(f"- [{timestamp}]() - [{new_title}] - {text}")
            new_content_parts.append("")  # Empty line after segment
            modified = True
            logging.info(f"Updated title: {new_title}")

        # Write back to file if modified
        if modified:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(new_content_parts))
            logging.info(f"Updated {file_path.name} with new titles.")
            return True
        else:
            logging.info(f"No segments to process in {file_path.name} (all protected or no segments found)")
            return False

    except Exception as e:
        logging.error(f"Error processing {file_path.name}: {e}")
        return False

def main():
    """Main function to process transcript files and generate titles."""
    logging.info("Starting LLM Title Generator...")

    # Test LLM connection first
    if not test_llm_connection():
        logging.error("Cannot connect to LLM server. Please ensure the server is running.")
        return

    # Choose folder
    folder_path = choose_folder()
    if not folder_path:
        return

    # Find markdown transcript files
    transcript_files = list(folder_path.glob("*.md"))

    if not transcript_files:
        logging.warning(f"No .md transcript files found in {folder_path}")
        return

    logging.info(f"Found {len(transcript_files)} transcript files.")

    # Print the list of files to be processed
    print(f"\n===== Transcript files to process ({len(transcript_files)}) =====")
    for i, file_path in enumerate(transcript_files, 1):
        print(f"{i}. {file_path.name}")
    print("=" * 50 + "\n")

    processed_count = 0
    updated_count = 0

    for file_path in transcript_files:
        try:
            if process_transcript_file(file_path):
                updated_count += 1
            processed_count += 1
        except Exception as e:
            logging.error(f"Critical error processing {file_path.name}: {e}")

    logging.info("="*20 + " Processing Summary " + "="*20)
    logging.info(f"Total transcript files found: {len(transcript_files)}")
    logging.info(f"Successfully processed: {processed_count}")
    logging.info(f"Files updated with new titles: {updated_count}")
    logging.info("Title generation complete.")

if __name__ == '__main__':
    main()

# YouTube Transcript Manager

A comprehensive one-click Python script that automatically manages YouTube video transcripts with audio download, transcription, and optional AI-generated titles.

## Features

- **Automatic Processing**: Processes YouTube URLs from an InputURL.txt file
- **Smart Duplicate Detection**: Skips videos that already have transcripts
- **High-Quality Transcription**: Uses OpenAI Whisper for accurate speech-to-text
- **Timestamped Chunks**: Creates 30-second chunks with clickable timestamp URLs
- **AI Title Generation**: Optional integration with local LLM for chunk titles
- **Automatic Cleanup**: Removes audio files and processed URLs automatically
- **YAML Front Matter**: Includes video metadata (title, author, date with time, URL)
- **Smart Date Formatting**: Uses YYYYMMDDHHmm format with actual upload time when available, or random time (0000-2359) when not

## Requirements

### Dependencies
```bash
pip install openai-whisper yt-dlp requests
```

### System Requirements
- **FFmpeg**: Required for audio processing
  - Windows: Download from https://ffmpeg.org/download.html
  - macOS: `brew install ffmpeg`
  - Linux: `sudo apt install ffmpeg`

### Optional: Local LLM Server
- For AI-generated chunk titles
- Default configuration: `http://127.0.0.1:7856`
- Compatible with OpenAI API format

## Setup

1. **Configure Folder Path** (Optional):
   ```python
   HARDCODED_FOLDER_PATH = "C:/Your/Transcript/Folder"
   ```
   If empty, the script will show a folder selection dialog.

2. **Create InputURL.txt**:
   Create a text file named `InputURL.txt` in your chosen folder with YouTube URLs (one per line):
   ```
   https://youtu.be/PKEwdWYLL5A
   https://www.youtube.com/watch?v=dQw4w9WgXcQ
   ```

## Usage

### One-Click Execution
```bash
python youtube_transcript_manager.py
```

### Workflow
1. **Folder Selection**: Choose folder containing InputURL.txt (or use hardcoded path)
2. **URL Processing**: For each URL in InputURL.txt:
   - Extract video metadata (title, author, date)
   - Check if transcript already exists → Skip if found
   - Download audio using yt-dlp
   - Transcribe audio using Whisper
   - Generate AI titles (if LLM available)
   - Create timestamped markdown transcript
   - Clean up audio file
   - Remove URL from InputURL.txt
3. **Completion**: Summary of processed videos

### Output Format

Each transcript is saved as `{Video_Title}.md` with:

```markdown
---
date: 202405121430
title: Example Video Title
author: Channel Name
url: https://youtu.be/PKEwdWYLL5A
---

- [00:00:00](https://youtu.be/PKEwdWYLL5A&t=0) - [Introduction to Topic] - Welcome to this video where we'll discuss...

- [00:00:30](https://youtu.be/PKEwdWYLL5A&t=30) - [Main Concept Explanation] - The key principle here is that...

- [00:01:00](https://youtu.be/PKEwdWYLL5A&t=60) - [Practical Examples] - Let me show you how this works in practice...
```

## Date Formatting

The script automatically formats upload dates in the YAML front matter using a smart time detection system:

### Format: YYYYMMDDHHmm
- **YYYY**: 4-digit year
- **MM**: 2-digit month
- **DD**: 2-digit day
- **HH**: 2-digit hour (00-23)
- **mm**: 2-digit minute (00-59)

### Time Detection Logic
1. **If hour/minute available**: Uses actual upload time from YouTube metadata
   - Sources: Unix timestamp, upload_time field
   - Example: `202405121430` (May 12, 2024 at 2:30 PM)

2. **If hour/minute not available**: Generates random time (0000-2359)
   - Ensures consistent 12-digit format
   - Example: `202405121847` (May 12, 2024 at random time 6:47 PM)

### Benefits
- **Consistent Format**: All dates are exactly 12 digits
- **Sortable**: Chronological sorting works correctly
- **Unique**: Random time component helps avoid filename conflicts
- **Readable**: Can be easily parsed back to human-readable format

## Configuration

### Transcription Settings
```python
CHUNK_SECONDS = 30          # Chunk duration in seconds
WHISPER_MODEL = "large-v3"  # Whisper model (base, small, medium, large, large-v3)
USE_FP16 = False           # GPU acceleration (requires CUDA)
```

### LLM Settings (Optional)
```python
LLM_BASE_URL = "http://127.0.0.1:7856"  # Local LLM server URL
LLM_MODEL = "qwen3-30b-a3b"             # Model name
```

## File Structure

```
Your_Folder/
├── InputURL.txt                    # Input URLs (processed URLs are removed)
├── Video_Title_1.md               # Generated transcripts
├── Video_Title_2.md
└── youtube_transcript_manager.py  # Main script
```

## Error Handling

- **Missing Dependencies**: Script checks for required packages and FFmpeg
- **Network Issues**: Graceful handling of download failures
- **Transcription Errors**: Continues processing other URLs on individual failures
- **File Conflicts**: Automatic filename sanitization for filesystem compatibility

## Troubleshooting

### Common Issues

1. **FFmpeg Not Found**:
   - Ensure FFmpeg is installed and in system PATH
   - Test with: `ffmpeg -version`

2. **Whisper Model Download**:
   - First run downloads the model (may take time)
   - Requires internet connection
   - Large models need sufficient RAM/VRAM

3. **LLM Connection Failed**:
   - Script continues without AI titles
   - Check LLM server is running on configured URL

4. **Audio Download Failed**:
   - Check YouTube URL validity
   - Some videos may have download restrictions
   - Verify yt-dlp is up to date: `pip install -U yt-dlp`

## Advanced Usage

### Custom Chunk Titles
If LLM is not available, you can manually edit titles in the generated markdown files. Titles starting with `-` are protected from being overwritten by the title generation script.

### Batch Processing
The script processes all URLs in InputURL.txt sequentially. For large batches, monitor system resources (RAM/VRAM for Whisper model).

### Integration with Existing Scripts
This script combines functionality from:
- `transcribe_folder.py` - Audio transcription
- `yt.py` - YouTube audio download
- `generate_titles.py` - AI title generation

## License

This script is provided as-is for educational and personal use.

#!/usr/bin/env python3
"""
YouTube Transcript Manager - One-Click Solution
Manages a folder of YouTube video transcripts with automatic processing.
"""

import os
import sys
import re
import json
import logging
import tempfile
import shutil
import subprocess
import requests
from pathlib import Path
from tkinter import filedialog, Tk
from typing import List, Optional, Dict, Any
from datetime import datetime

try:
    import whisper
    import yt_dlp
except ImportError as e:
    print(f"Missing required dependency: {e}")
    print("Please install: pip install openai-whisper yt-dlp")
    sys.exit(1)

# --- Configuration ---
# Hardcoded folder path - set this to your preferred folder or leave empty for dialog
HARDCODED_FOLDER_PATH = ""  # Example: "C:/Users/<USER>/Videos/Transcripts"

# File extensions
VIDEO_EXTENSIONS = ['.mp4', '.mov', '.mkv', '.avi', '.wmv', '.flv']
AUDIO_EXTENSIONS = ['.mp3', '.wav', '.m4a', '.aac', '.ogg', '.flac']
MEDIA_EXTENSIONS = set(VIDEO_EXTENSIONS + AUDIO_EXTENSIONS)

# Transcription settings
TRANSCRIPT_FORMAT = 'md'
CHUNK_SECONDS = 30
WHISPER_MODEL = "large-v3"
USE_FP16 = False

# LLM settings for title generation
LLM_BASE_URL = "http://127.0.0.1:7856"
LLM_MODEL = "qwen3-30b-a3b"
PROTECTED_TITLE_PREFIX = "-"

# File names
INPUT_URL_FILENAME = "InputURL.txt"

# --- Setup Logging ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)

def check_dependencies() -> bool:
    """Check if required dependencies are available."""
    missing = []
    
    if shutil.which("ffmpeg") is None:
        missing.append("ffmpeg")
    if shutil.which("ffprobe") is None:
        missing.append("ffprobe")
    
    if missing:
        logging.error(f"Missing dependencies: {', '.join(missing)}")
        logging.error("Please install ffmpeg and ensure it's in your system's PATH.")
        return False
    
    logging.info("All dependencies found.")
    return True

def choose_folder() -> Optional[Path]:
    """Opens a dialog to choose a folder or uses hardcoded path."""
    if HARDCODED_FOLDER_PATH and Path(HARDCODED_FOLDER_PATH).exists():
        logging.info(f"Using hardcoded folder: {HARDCODED_FOLDER_PATH}")
        return Path(HARDCODED_FOLDER_PATH)
    
    root = Tk()
    root.withdraw()
    folder_path = filedialog.askdirectory(title="Select Folder Containing InputURL.txt")
    root.destroy()
    
    if folder_path:
        logging.info(f"Selected folder: {folder_path}")
        return Path(folder_path)
    else:
        logging.warning("No folder selected.")
        return None

def find_input_url_file(folder_path: Path) -> Optional[Path]:
    """Find the InputURL.txt file in the folder."""
    input_file = folder_path / INPUT_URL_FILENAME
    if input_file.exists():
        logging.info(f"Found InputURL file: {input_file}")
        return input_file
    else:
        logging.warning(f"InputURL file not found: {input_file}")
        return None

def read_urls_from_file(input_file: Path) -> List[str]:
    """Read URLs from the InputURL file."""
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            urls = [line.strip() for line in f if line.strip()]
        logging.info(f"Found {len(urls)} URLs in InputURL file")
        return urls
    except Exception as e:
        logging.error(f"Error reading InputURL file: {e}")
        return []

def extract_video_metadata(url: str) -> Dict[str, Any]:
    """Extract video metadata using yt-dlp."""
    ydl_opts = {
        'quiet': True,
        'no_warnings': True,
        'extract_flat': False,
    }
    
    try:
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(url, download=False)
            
            # Extract relevant metadata
            metadata = {
                'title': info.get('title', 'Unknown Title'),
                'uploader': info.get('uploader', 'Unknown Author'),
                'upload_date': info.get('upload_date', ''),
                'url': url,
                'duration': info.get('duration', 0)
            }
            
            # Format upload date
            if metadata['upload_date']:
                try:
                    date_obj = datetime.strptime(metadata['upload_date'], '%Y%m%d')
                    metadata['formatted_date'] = date_obj.strftime('%Y-%m-%d')
                except:
                    metadata['formatted_date'] = metadata['upload_date']
            else:
                metadata['formatted_date'] = ''
            
            return metadata
    except Exception as e:
        logging.error(f"Error extracting metadata for {url}: {e}")
        return {
            'title': 'Unknown Title',
            'uploader': 'Unknown Author',
            'upload_date': '',
            'formatted_date': '',
            'url': url,
            'duration': 0
        }

def sanitize_filename(filename: str) -> str:
    """Sanitize filename for filesystem compatibility."""
    # Remove or replace invalid characters
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    
    # Limit length
    if len(filename) > 200:
        filename = filename[:200]
    
    return filename.strip()

def transcript_exists(folder_path: Path, video_title: str) -> bool:
    """Check if a transcript already exists for the video."""
    safe_title = sanitize_filename(video_title)
    transcript_file = folder_path / f"{safe_title}.{TRANSCRIPT_FORMAT}"
    return transcript_file.exists()

def download_youtube_audio(url: str, output_dir: Path) -> Optional[Path]:
    """Download audio from YouTube URL."""
    ydl_opts = {
        'format': 'm4a/bestaudio/best',
        'postprocessors': [{
            'key': 'FFmpegExtractAudio',
            'preferredcodec': 'm4a',
        }],
        'outtmpl': str(output_dir / '%(title)s.%(ext)s'),
        'noplaylist': True,
        'quiet': True,
    }
    
    try:
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            # Extract info first to get the filename
            info = ydl.extract_info(url, download=False)
            title = info.get('title', 'Unknown')
            safe_title = sanitize_filename(title)
            
            # Download the audio
            ydl.download([url])
            
            # Find the downloaded file
            for ext in ['.m4a', '.mp3', '.wav']:
                audio_file = output_dir / f"{title}{ext}"
                if audio_file.exists():
                    return audio_file
            
            # Fallback: look for any audio file with similar name
            for file in output_dir.glob(f"{safe_title}*"):
                if file.suffix.lower() in AUDIO_EXTENSIONS:
                    return file
                    
        logging.error(f"Downloaded audio file not found for: {url}")
        return None
        
    except Exception as e:
        logging.error(f"Error downloading audio from {url}: {e}")
        return None

def format_time(seconds: float) -> str:
    """Convert seconds to HH:MM:SS format."""
    try:
        if seconds < 0:
            seconds = 0
        
        integer_seconds = int(seconds)
        hours, remainder = divmod(integer_seconds, 3600)
        minutes, secs = divmod(remainder, 60)
        return f'{hours:02}:{minutes:02}:{secs:02}'
    except:
        return '00:00:00'

def create_timestamp_url(base_url: str, seconds: float) -> str:
    """Create a timestamped URL for the video."""
    try:
        timestamp = int(seconds)
        if '&' in base_url:
            return f"{base_url}&t={timestamp}"
        else:
            return f"{base_url}&t={timestamp}"
    except:
        return base_url

def get_audio_stream_indices(media_path: Path) -> List[int]:
    """Returns a list of audio stream indices using ffprobe."""
    command = [
        'ffprobe', '-v', 'error', '-select_streams', 'a',
        '-show_entries', 'stream=index', '-of', 'csv=p=0', str(media_path)
    ]
    try:
        result = subprocess.run(
            command,
            stdout=subprocess.PIPE, stderr=subprocess.PIPE,
            text=True, check=True, encoding='utf-8'
        )
        indices = [int(line.strip()) for line in result.stdout.strip().split('\n') if line.strip().isdigit()]
        if not indices:
            logging.warning(f"No audio streams found by ffprobe for: {media_path.name}")
        return indices
    except subprocess.CalledProcessError as e:
        logging.error(f"ffprobe failed for {media_path.name}. Error: {e.stderr.strip()}")
        return []
    except Exception as e:
        logging.error(f"An unexpected error occurred during ffprobe execution for {media_path.name}: {e}")
        return []

def extract_audio_track(media_path: Path, stream_index: int, temp_dir: Path) -> Optional[Path]:
    """Extracts a specific audio stream to a WAV file."""
    safe_media_name = "".join(c if c.isalnum() else "_" for c in media_path.stem)
    audio_filename = f"{safe_media_name}_track_{stream_index}.wav"
    audio_path = temp_dir / audio_filename

    command = [
        'ffmpeg', '-y', '-i', str(media_path),
        '-map', f'0:{stream_index}',
        '-ac', '1',         # Mono channel
        '-ar', '16000',      # 16kHz sample rate (optimal for Whisper)
        '-vn',              # No video output
        '-acodec', 'pcm_s16le', # WAV codec
        str(audio_path)
    ]
    try:
        logging.info(f"Extracting audio track (index {stream_index}) from {media_path.name}...")
        result = subprocess.run(
            command,
            stdout=subprocess.PIPE, stderr=subprocess.PIPE,
            text=True, check=False, encoding='utf-8'
        )
        if result.returncode != 0:
            logging.error(f"ffmpeg failed to extract track {stream_index} from {media_path.name}. Error: {result.stderr.strip()}")
            return None
        if not audio_path.exists() or audio_path.stat().st_size == 0:
            logging.warning(f"Extracted audio file for stream {stream_index} is missing or empty: {audio_path}")
            return None
        logging.info(f"Successfully extracted audio to: {audio_path.name}")
        return audio_path
    except FileNotFoundError:
        logging.error(f"ffmpeg command failed. Is ffmpeg installed and in PATH? Command: {' '.join(command)}")
        return None
    except Exception as e:
        logging.error(f"An unexpected error occurred during ffmpeg extraction for track {stream_index} of {media_path.name}: {e}")
        return None

def test_llm_connection() -> bool:
    """Test if the LLM server is accessible."""
    try:
        response = requests.get(f"{LLM_BASE_URL}/v1/models", timeout=5)
        if response.status_code == 200:
            logging.info("LLM server is accessible.")
            return True
        else:
            logging.warning(f"LLM server returned status {response.status_code}")
            return False
    except Exception as e:
        logging.warning(f"Cannot connect to LLM server: {e}")
        return False

def generate_title_with_llm(text: str) -> str:
    """Generate a concise title for the given text using the local LLM."""
    if not text.strip():
        return "No Content"

    try:
        prompt = f"""Create a concise, descriptive title (5-20 words) for this transcript segment. The title should capture the main topic or key concept discussed. Return only the title on a single line, no quotes, no extra text, no newlines.

Transcript: {text.strip()}

Title:"""

        response = requests.post(
            f"{LLM_BASE_URL}/v1/chat/completions",
            headers={"Content-Type": "application/json"},
            json={
                "model": LLM_MODEL,
                "messages": [
                    {"role": "user", "content": prompt}
                ],
                "max_tokens": 1000,
                "temperature": 0.5
            },
            timeout=30
        )

        if response.status_code == 200:
            result = response.json()
            raw_content = result["choices"][0]["message"]["content"].strip()

            # Remove thinking tags and content
            title = re.sub(r'<think>.*?</think>', '', raw_content, flags=re.DOTALL).strip()

            # Clean up the title
            title = title.strip('"\'').strip()
            title = re.sub(r'\s+', ' ', title).strip()

            # Limit title length
            if len(title) > 100:
                title = title[:97] + "..."

            return title if title else "Untitled Segment"
        else:
            logging.warning(f"LLM request failed with status {response.status_code}")
            return "Untitled Segment"

    except Exception as e:
        logging.warning(f"Failed to generate title with LLM: {e}")
        return "Untitled Segment"

def transcribe_audio_file(audio_path: Path, metadata: Dict[str, Any], model, use_llm: bool = False) -> Optional[Path]:
    """Transcribe audio file and create markdown transcript."""
    logging.info(f"Transcribing: {audio_path.name}")

    try:
        # Transcribe with Whisper
        result = model.transcribe(str(audio_path), fp16=USE_FP16)
        segments = result.get('segments', [])

        if not segments:
            logging.warning(f"No segments found in transcription for {audio_path.name}")
            return None

        # Create output filename
        safe_title = sanitize_filename(metadata['title'])
        output_filename = f"{safe_title}.{TRANSCRIPT_FORMAT}"
        output_path = audio_path.parent / output_filename

        logging.info(f"Writing transcript to: {output_path}")

        with open(output_path, 'w', encoding='utf-8') as f:
            # Write YAML front matter
            f.write("---\n")
            f.write(f"date: {metadata['formatted_date']}\n")
            f.write(f"title: {metadata['title']}\n")
            f.write(f"author: {metadata['uploader']}\n")
            f.write(f"url: {metadata['url']}\n")
            f.write("---\n\n")

            # Determine total duration for chunking
            max_segment_end_time = 0
            if segments:
                valid_end_times = [s.get('end') for s in segments if s.get('end') is not None]
                if valid_end_times:
                    max_segment_end_time = max(valid_end_times)
                else:
                    valid_start_times = [s.get('start') for s in segments if s.get('start') is not None]
                    if valid_start_times:
                        max_segment_end_time = max(valid_start_times)

            num_total_chunks = (int(max_segment_end_time) // CHUNK_SECONDS) + 1

            for i in range(num_total_chunks):
                chunk_start_seconds = float(i * CHUNK_SECONDS)
                chunk_end_seconds = float((i + 1) * CHUNK_SECONDS)

                # Define extended window for transcription (5 seconds padding)
                extended_start = max(0, chunk_start_seconds - 5.0)
                extended_end = chunk_end_seconds + 5.0

                current_chunk_texts = []

                for segment in segments:
                    seg_start = segment.get('start')
                    seg_end = segment.get('end')
                    text = segment.get('text', '').strip()

                    if seg_start is not None and seg_end is not None and text:
                        # Include segments that overlap with the extended window
                        if ((extended_start <= seg_start < extended_end) or
                            (extended_start <= seg_end < extended_end) or
                            (seg_start <= extended_start and seg_end >= extended_end)):
                            current_chunk_texts.append(text)

                full_text_for_chunk = " ".join(current_chunk_texts).strip()

                if full_text_for_chunk:  # Only write entries with content
                    # Format timestamp
                    timestamp = format_time(chunk_start_seconds)
                    timestamp_url = create_timestamp_url(metadata['url'], chunk_start_seconds)

                    # Generate title if LLM is available
                    if use_llm:
                        chunk_title = generate_title_with_llm(full_text_for_chunk)
                    else:
                        chunk_title = "Untitled Segment"

                    # Write markdown entry
                    f.write(f"- [{timestamp}]({timestamp_url}) - [{chunk_title}] - {full_text_for_chunk}\n\n")

        logging.info(f"Transcript created successfully: {output_path}")
        return output_path

    except Exception as e:
        logging.error(f"Error transcribing {audio_path.name}: {e}")
        return None

def remove_url_from_file(input_file: Path, url_to_remove: str):
    """Remove a specific URL from the InputURL file."""
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        # Filter out the URL to remove
        updated_lines = [line for line in lines if line.strip() != url_to_remove]

        with open(input_file, 'w', encoding='utf-8') as f:
            f.writelines(updated_lines)

        logging.info(f"Removed URL from InputURL file: {url_to_remove}")

    except Exception as e:
        logging.error(f"Error removing URL from file: {e}")

def cleanup_audio_file(audio_path: Path):
    """Delete the audio file after processing."""
    try:
        if audio_path.exists():
            audio_path.unlink()
            logging.info(f"Deleted audio file: {audio_path.name}")
    except Exception as e:
        logging.error(f"Error deleting audio file {audio_path.name}: {e}")

def process_single_url(url: str, folder_path: Path, input_file: Path, model, use_llm: bool) -> bool:
    """Process a single YouTube URL."""
    logging.info(f"Processing URL: {url}")

    try:
        # Extract video metadata
        metadata = extract_video_metadata(url)
        video_title = metadata['title']

        # Check if transcript already exists
        if transcript_exists(folder_path, video_title):
            logging.info(f"Transcript already exists for: {video_title}")
            remove_url_from_file(input_file, url)
            return True

        # Download audio
        logging.info(f"Downloading audio for: {video_title}")
        audio_path = download_youtube_audio(url, folder_path)

        if not audio_path:
            logging.error(f"Failed to download audio for: {video_title}")
            return False

        # Transcribe audio
        logging.info(f"Transcribing audio for: {video_title}")
        transcript_path = transcribe_audio_file(audio_path, metadata, model, use_llm)

        if transcript_path:
            # Clean up audio file
            cleanup_audio_file(audio_path)

            # Remove URL from InputURL file
            remove_url_from_file(input_file, url)

            logging.info(f"Successfully processed: {video_title}")
            return True
        else:
            logging.error(f"Failed to transcribe: {video_title}")
            cleanup_audio_file(audio_path)  # Clean up even on failure
            return False

    except Exception as e:
        logging.error(f"Error processing URL {url}: {e}")
        return False

def main():
    """Main function to manage YouTube transcript processing."""
    logging.info("=== YouTube Transcript Manager Started ===")

    # Check dependencies
    if not check_dependencies():
        logging.error("Missing required dependencies. Exiting.")
        return

    # Choose folder
    folder_path = choose_folder()
    if not folder_path:
        logging.error("No folder selected. Exiting.")
        return

    # Find InputURL file
    input_file = find_input_url_file(folder_path)
    if not input_file:
        logging.error(f"InputURL.txt file not found in {folder_path}. Exiting.")
        return

    # Read URLs from file
    urls = read_urls_from_file(input_file)
    if not urls:
        logging.info("No URLs found in InputURL file. Nothing to process.")
        return

    logging.info(f"Found {len(urls)} URLs to process")

    # Test LLM connection
    use_llm = test_llm_connection()
    if use_llm:
        logging.info("LLM server available - will generate chunk titles")
    else:
        logging.info("LLM server not available - using default titles")

    # Load Whisper model
    try:
        logging.info(f"Loading Whisper model: {WHISPER_MODEL}")
        logging.info("This may take some time for larger models...")
        model = whisper.load_model(WHISPER_MODEL)
        logging.info("Whisper model loaded successfully")
    except Exception as e:
        logging.error(f"Failed to load Whisper model: {e}")
        return

    # Process each URL
    processed_count = 0
    failed_count = 0

    logging.info("=== Starting URL Processing ===")

    for i, url in enumerate(urls, 1):
        logging.info(f"Processing {i}/{len(urls)}: {url}")

        try:
            if process_single_url(url, folder_path, input_file, model, use_llm):
                processed_count += 1
                logging.info(f"✓ Successfully processed URL {i}/{len(urls)}")
            else:
                failed_count += 1
                logging.error(f"✗ Failed to process URL {i}/{len(urls)}")
        except Exception as e:
            failed_count += 1
            logging.error(f"✗ Critical error processing URL {i}/{len(urls)}: {e}")

    # Summary
    logging.info("=== Processing Summary ===")
    logging.info(f"Total URLs found: {len(urls)}")
    logging.info(f"Successfully processed: {processed_count}")
    logging.info(f"Failed: {failed_count}")

    if processed_count > 0:
        logging.info(f"Transcripts saved to: {folder_path}")

    logging.info("=== YouTube Transcript Manager Finished ===")

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        logging.info("Process interrupted by user")
    except Exception as e:
        logging.error(f"Unexpected error: {e}", exc_info=True)
    finally:
        logging.info("Script execution completed")
